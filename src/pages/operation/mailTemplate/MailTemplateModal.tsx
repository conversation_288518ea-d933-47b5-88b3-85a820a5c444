import { useTranslation } from 'react-i18next';

import FormModal from '@/components/FormModal';
import HTMLEditor from '@/components/HTMLEditor';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import { MailTemplate } from '@/types/operation';

import useMailTemplateCategories from './hooks/useMailTemplateCategories';
import useMailTemplateMutation from './hooks/useMailTemplateMutation';
import useMailTemplateTypes from './hooks/useMailTemplateTypes';

type MailTemplateModalProps = {
  open: boolean;
  onClose: () => void;
  initialValues?: Partial<MailTemplate>;
};

const MailTemplateModal = ({ open, onClose, initialValues }: MailTemplateModalProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm();

  const isEdit = !!initialValues?.id;

  // Fetch categories and types
  const { data: categories } = useMailTemplateCategories();
  const { data: types } = useMailTemplateTypes();
  const { create: createMutation, update: updateMutation } = useMailTemplateMutation();

  const handleSubmit = (values: any) => {
    const formData = {
      templateId: values.templateId,
      title: values.title,
      content: values.content,
      description: values.description,
      category: values.category,
      type: values.type
    };

    const mutation = isEdit && initialValues?.id ? updateMutation : createMutation;

    mutation.mutate(
      isEdit && initialValues?.id ? { ...formData, id: initialValues.id } : formData,
      {
        onSuccess: () => {
          onClose();
          form.resetFields();
        }
      }
    );
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const categoryOptions = categories?.map(cat => ({
    label: cat.name,
    value: cat.id
  })) || [];

  const typeOptions = types?.map(type => ({
    label: type.name,
    value: type.id
  })) || [];

  return (
    <FormModal
      title={t(isEdit ? 'common_edit_name' : 'common_add_name', { name: t('pages_mailTemplate') })}
      form={form}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={createMutation.isPending || updateMutation.isPending}
      initialValues={{
        templateId: initialValues?.templateId || '',
        title: initialValues?.title || '',
        content: initialValues?.content || '',
        description: initialValues?.description || '',
        category: initialValues?.category,
        type: initialValues?.type
      }}
    >
      <RForm.Item
        name="templateId"
        label={t('pages_mailTemplate_templateId')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RInput placeholder={t('pages_mailTemplate_templateId_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="title"
        label={t('pages_mailTemplate_title')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RInput placeholder={t('pages_mailTemplate_title_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="category"
        label={t('pages_mailTemplate_category')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RSelect options={categoryOptions} placeholder={t('pages_mailTemplate_category_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="type"
        label={t('pages_mailTemplate_type')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RSelect options={typeOptions} placeholder={t('pages_mailTemplate_type_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="description"
        label={t('pages_mailTemplate_description')}
      >
        <RInput placeholder={t('pages_mailTemplate_description_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="content"
        label={t('pages_mailTemplate_content')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <HTMLEditor />
      </RForm.Item>
    </FormModal>
  );
};

export default MailTemplateModal;
