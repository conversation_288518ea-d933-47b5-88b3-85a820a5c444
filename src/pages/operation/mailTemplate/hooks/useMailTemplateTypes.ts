import { useQuery } from '@tanstack/react-query';

import { getMailTemplateTypes } from '@/api/operation';

// Query keys
export const mailTemplateTypeKeys = {
  all: ['mailTemplateType'] as const,
  lists: () => [...mailTemplateTypeKeys.all, 'list'] as const,
  details: () => [...mailTemplateTypeKeys.all, 'detail'] as const,
  detail: (id: number | string) => [...mailTemplateTypeKeys.details(), id] as const
};

const useMailTemplateTypes = () => {
  const query = useQuery({
    queryKey: mailTemplateTypeKeys.lists(),
    queryFn: () => getMailTemplateTypes(),
    select: (data) => data.data || []
  });

  return query;
};

export default useMailTemplateTypes;
