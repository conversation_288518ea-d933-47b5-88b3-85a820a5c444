import { useTranslation } from 'react-i18next';

import OperatorCell from '@/components/cells/OperatorCell';
import RButton from '@/components/RButton';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import useConfirmModal from '@/hooks/useConfirmModal';
import { MailTemplate } from '@/types/operation';

import useMailTemplate from './hooks/useMailTemplate';
import useMailTemplateMutation from './hooks/useMailTemplateMutation';

type MailTemplateTableProps = {
  setInitialValues: (values: MailTemplate) => void;
  setOpen: (open: boolean) => void;
  searchParams?: {
    templateId?: string;
    title?: string;
    category?: number;
    type?: number;
  };
};

const MailTemplateTable = ({ setInitialValues, setOpen, searchParams }: MailTemplateTableProps) => {
  const { t } = useTranslation();
  const { confirmModal } = useConfirmModal();

  const mailTemplateQuery = useMailTemplate(searchParams);
  const mailTemplateList = mailTemplateQuery.data || [];
  const isPending = mailTemplateQuery.isPending;
  const { delete: deleteMutation } = useMailTemplateMutation();

  const handleEdit = (record: MailTemplate) => {
    setInitialValues(record);
    setOpen(true);
  };

  const handleDelete = (record: MailTemplate) => {
    confirmModal({
      content: t('common_confirm_delete'),
      onOk: () => deleteMutation.mutate({ id: record.id })
    });
  };

  const columns = [
    {
      title: t('pages_mailTemplate_templateId'),
      dataIndex: 'templateId',
      key: 'template_id',
      width: '12%'
    },
    {
      title: t('pages_mailTemplate_title'),
      dataIndex: 'title',
      key: 'title',
      width: '20%'
    },
    {
      title: t('pages_mailTemplate_category'),
      dataIndex: 'categoryLabel',
      key: 'categoryLabel',
      width: '12%',
      render: (categoryLabel: string) => (
        <RTag color="blue">{categoryLabel}</RTag>
      )
    },
    {
      title: t('pages_mailTemplate_type'),
      dataIndex: 'typeLabel',
      key: 'typeLabel',
      width: '12%',
      render: (typeLabel: string) => (
        <RTag color="green">{typeLabel}</RTag>
      )
    },
    {
      title: t('pages_mailTemplate_description'),
      dataIndex: 'description',
      key: 'description',
      width: '15%',
      render: (description: string) => (
        <span className="truncate" title={description}>
          {description || '-'}
        </span>
      )
    },
    {
      title: t('common_createdBy'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: '12%',
      render: (_: string, record: MailTemplate) => (
        <OperatorCell
          record={{ updatedBy: record.createdBy, updatedAt: record.createdAt }}
        />
      )
    },
    {
      title: t('common_action'),
      key: 'action',
      width: '17%',
      render: (_: unknown, record: MailTemplate) => (
        <div className="flex gap-2">
          <RButton
            size="small"
            variant="outlined"
            color="primary"
            type="link"
            onClick={() => handleEdit(record)}
          >
            {t('common_edit')}
          </RButton>
          <RButton
            size="small"
            variant="outlined"
            color="red"
            type="link"
            onClick={() => handleDelete(record)}
          >
            {t('common_delete')}
          </RButton>
        </div>
      )
    }
  ];

  return (
    <RTable
      loading={isPending}
      rowKey="id"
      dataSource={mailTemplateList}
      columns={columns}
      pagination={false}
    />
  );
};

export default MailTemplateTable;
