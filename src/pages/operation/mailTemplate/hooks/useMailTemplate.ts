import { useQuery } from '@tanstack/react-query';

import { getMailTemplateList } from '@/api/operation';
import { MailTemplate } from '@/types/operation';

// Query keys
export const mailTemplateKeys = {
  all: ['mailTemplate'] as const,
  lists: () => [...mailTemplateKeys.all, 'list'] as const,
  list: (filters?: Record<string, any>) => [...mailTemplateKeys.lists(), filters] as const,
  details: () => [...mailTemplateKeys.all, 'detail'] as const,
  detail: (id: number | string) => [...mailTemplateKeys.details(), id] as const
};

type UseMailTemplateParams = {
  templateId?: string;
  title?: string;
  category?: number;
  type?: number;
};

const useMailTemplate = (params?: UseMailTemplateParams) => {
  const query = useQuery({
    queryKey: mailTemplateKeys.list(params),
    queryFn: () => getMailTemplateList(),
    select: (data): MailTemplate[] => data.data?.data || []
  });

  return query;
};

export default useMailTemplate;
