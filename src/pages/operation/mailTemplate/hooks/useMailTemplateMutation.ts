import { useMutation, useQueryClient } from '@tanstack/react-query';

import { createMailTemplate, updateMailTemplate, deleteMailTemplate } from '@/api/operation';

import { mailTemplateKeys } from './useMailTemplate';

const useMailTemplateMutation = () => {
  const queryClient = useQueryClient();

  const createMutation = useMutation({
    mutationFn: createMailTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: mailTemplateKeys.lists() });
    }
  });

  const updateMutation = useMutation({
    mutationFn: updateMailTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: mailTemplateKeys.lists() });
    }
  });

  const deleteMutation = useMutation({
    mutationFn: deleteMailTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: mailTemplateKeys.lists() });
    }
  });

  return {
    create: createMutation,
    update: updateMutation,
    delete: deleteMutation
  };
};

export default useMailTemplateMutation;
