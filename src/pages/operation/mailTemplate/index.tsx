import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import SearchForm from '@/components/SearchForm';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { MailTemplate } from '@/types/operation';

import useMailTemplateCategories from './hooks/useMailTemplateCategories';
import useMailTemplateTypes from './hooks/useMailTemplateTypes';
import MailTemplateModal from './MailTemplateModal';
import MailTemplateTable from './MailTemplateTable';

type SearchFormValues = {
  templateId?: string;
  title?: string;
  category?: number;
  type?: number;
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();
  const { data: categories } = useMailTemplateCategories();
  const { data: types } = useMailTemplateTypes();

  const categoryOptions = [
    { label: t('common_all'), value: undefined },
    ...(categories?.map((cat) => ({
      label: cat.name,
      value: cat.id
    })) || [])
  ];

  const typeOptions = [
    { label: t('common_all'), value: undefined },
    ...(types?.map((type) => ({
      label: type.name,
      value: type.id
    })) || [])
  ];

  return (
    <SearchForm<SearchFormValues> onSearch={onSearch} onReset={onReset} className="">
      <RForm.Item name="templateId" label={t('pages_mailTemplate_templateId')}>
        <RInput placeholder={t('pages_mailTemplate_templateId_placeholder')} />
      </RForm.Item>
      <RForm.Item name="title" label={t('pages_mailTemplate_title')}>
        <RInput placeholder={t('pages_mailTemplate_title_placeholder')} />
      </RForm.Item>
      <RForm.Item name="category" label={t('pages_mailTemplate_category')}>
        <RSelect
          options={categoryOptions}
          placeholder={t('pages_mailTemplate_category_placeholder')}
        />
      </RForm.Item>
      <RForm.Item name="type" label={t('pages_mailTemplate_type')}>
        <RSelect options={typeOptions} placeholder={t('pages_mailTemplate_type_placeholder')} />
      </RForm.Item>
    </SearchForm>
  );
};

const MailTemplatePage = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<MailTemplate>();
  const [searchParams, setSearchParams] = useState<SearchFormValues>({});

  const handleAdd = () => {
    setOpen(true);
    setInitialValues(undefined);
  };

  const handleSearch = (values: SearchFormValues) => {
    setSearchParams(values);
  };

  const handleReset = () => {
    setSearchParams({});
  };

  return (
    <>
      <TableSearchLayout
        searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleReset} />}
      >
        <RButton className="mb-4" onClick={handleAdd}>
          {t('common_add_name', { name: t('pages_mailTemplate') })}
        </RButton>
        <MailTemplateTable
          setInitialValues={setInitialValues}
          setOpen={setOpen}
          searchParams={searchParams}
        />
      </TableSearchLayout>
      <MailTemplateModal open={open} onClose={() => setOpen(false)} initialValues={initialValues} />
    </>
  );
};

export default MailTemplatePage;
