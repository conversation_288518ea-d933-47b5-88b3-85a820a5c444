import { useQuery } from '@tanstack/react-query';

import { getMailTemplateCategories } from '@/api/operation';

// Query keys
export const mailTemplateCategoryKeys = {
  all: ['mailTemplateCategory'] as const,
  lists: () => [...mailTemplateCategoryKeys.all, 'list'] as const,
  details: () => [...mailTemplateCategoryKeys.all, 'detail'] as const,
  detail: (id: number | string) => [...mailTemplateCategoryKeys.details(), id] as const
};

const useMailTemplateCategories = () => {
  const query = useQuery({
    queryKey: mailTemplateCategoryKeys.lists(),
    queryFn: () => getMailTemplateCategories(),
    select: (data) => data.data || []
  });

  return query;
};

export default useMailTemplateCategories;
